"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Download,
  ExternalLink,
  Phone,
  Shield,
  XCircle,
  Zap,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import LiquidGlass from "liquid-glass-react";

interface AppDetailsModalProps {
  app: App | null;
  isOpen: boolean;
  onClose: () => void;
}

export const AppDetailsModal = ({
  app,
  isOpen,
  onClose,
}: AppDetailsModalProps) => {
  if (!app) return null;

  const SecurityFeature = ({
    icon: Icon,
    title,
    enabled,
    link,
    description,
    color,
  }: {
    icon: React.ElementType;
    title: string;
    enabled: boolean;
    link?: string;
    description: string;
    color: string;
  }) => (
    <div className="group relative overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-primary/10 hover:border-primary/30">
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div
        className={`absolute top-4 right-4 w-3 h-3 rounded-full ${
          enabled
            ? "bg-green-400 shadow-lg shadow-green-400/50"
            : "bg-red-400 shadow-lg shadow-red-400/50"
        } animate-pulse`}
      />

      <div className="relative z-10">
        <div className="flex items-start space-x-4 mb-4">
          <div className={`rounded-xl p-3 ${color} shadow-lg`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-lg text-white mb-1">{title}</h3>
            <p className="text-sm text-neutral-300 leading-relaxed">
              {description}
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <div className="flex items-center space-x-2 ">
            {enabled ? (
              <CheckCircle className="w-5 h-5 text-green-400" />
            ) : (
              <XCircle className="w-5 h-5 text-red-400" />
            )}
            <Badge
              variant={enabled ? "default" : "destructive"}
              className={`text-xs font-medium px-3 py-1 ${
                enabled
                  ? "bg-green-500/20 text-green-300 border-green-500/30"
                  : "bg-red-500/20 text-red-300 border-red-500/30"
              }`}
            >
              {enabled ? "Supported" : "Not Available"}
            </Badge>
          </div>

          {enabled && link && (
            <Button
              asChild
              size="sm"
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 transition-all duration-200"
            >
              <Link href={link} target="_blank" className="flex items-center">
                <ExternalLink className="w-3 h-3 mr-2" />
                Configure
              </Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full sm:min-w-[50vw] max-h-[95vh] overflow-y-auto border-white/10 shadow-2xl">
        <LiquidGlass
          displacementScale={50}
          blurAmount={0.08}
          saturation={120}
          aberrationIntensity={1.5}
          elasticity={0.25}
          cornerRadius={16}
          overLight={false}
          className="w-full h-full"
        >
          <DialogHeader className="space-y-6 pb-8 border-b border-white/10">
            <div className="flex items-start space-x-6">
              {app.appLogo ? (
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                  <Image
                    src={app.appLogo}
                    alt={`${app.name} logo`}
                    width={80}
                    height={80}
                    className="relative w-20 h-20 object-contain rounded-2xl shadow-2xl border border-white/10 bg-white/5 backdrop-blur-sm"
                  />
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center shadow-lg border-2 border-neutral-900">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                </div>
              ) : (
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                  <div className="relative w-20 h-20 bg-gradient-to-br from-primary via-primary/90 to-primary/70 rounded-2xl flex items-center justify-center shadow-2xl border border-white/10">
                    <span className="text-3xl font-bold text-white">
                      {app.name.charAt(0)}
                    </span>
                  </div>
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center shadow-lg border-2 border-neutral-900">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
              <div className="flex-1 space-y-3">
                <DialogTitle className="text-4xl font-bold bg-gradient-to-r from-white via-white to-neutral-300 bg-clip-text text-transparent">
                  {app.name}
                </DialogTitle>
                <Badge
                  variant="secondary"
                  className="text-sm px-4 py-2 bg-white/10 text-white border-white/20 backdrop-blur-sm hover:bg-white/20 transition-colors"
                >
                  {APP_TYPE_TEXT[app.type]}
                </Badge>
                <p className="text-neutral-400 text-sm leading-relaxed max-w-2xl">
                  Comprehensive security evaluation and configuration management
                  for your {app.name} account.
                </p>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-12 pt-2">
            <div>
              <div className="flex items-center space-x-3 mb-8">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Shield className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white">
                  Security Features
                </h2>
                <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <SecurityFeature
                  icon={Shield}
                  title="Yubikey Support"
                  enabled={app.yubikeys?.enabled ?? false}
                  link={app.yubikeys.link}
                  description="Hardware security key authentication for enhanced protection"
                  color="bg-gradient-to-r from-blue-500 to-blue-600"
                />
                <SecurityFeature
                  icon={Phone}
                  title="Phone Number Required"
                  enabled={app.phoneNumber?.enabled ?? false}
                  link={app.phoneNumber?.link}
                  description="Phone number dependency for 2FA verification"
                  color={
                    app.phoneNumber?.enabled
                      ? "bg-gradient-to-r from-red-500 to-red-600"
                      : "bg-gradient-to-r from-green-500 to-green-600"
                  }
                />
                <SecurityFeature
                  icon={Download}
                  title="Backup Codes"
                  enabled={app.backup?.enabled ?? false}
                  link={app.backup?.link}
                  description="Recovery codes for emergency account access"
                  color="bg-gradient-to-r from-purple-500 to-purple-600"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center space-x-3 mb-8">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white">Quick Actions</h2>
                <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {app.healthAccount && (
                  <div className="group relative overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-br from-blue-900/30 via-blue-800/20 to-blue-900/30 backdrop-blur-sm p-8 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-blue-500/20 hover:border-blue-400/30">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400 rounded-full animate-pulse" />

                    <div className="relative z-10">
                      <div className="flex items-start space-x-4 mb-6">
                        <div className="rounded-xl p-3 bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
                          <Activity className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-bold text-xl text-white mb-2">
                            Security Health Check
                          </h3>
                          <p className="text-blue-200 text-sm leading-relaxed">
                            Review and optimize your account security settings
                            for maximum protection
                          </p>
                        </div>
                      </div>
                      <Button
                        asChild
                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                      >
                        <Link
                          href={app.healthAccount}
                          target="_blank"
                          className="flex items-center justify-center"
                        >
                          <Activity className="w-5 h-5 mr-3" />
                          Open Security Settings
                          <ExternalLink className="w-4 h-4 ml-2 opacity-70" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                )}

                {app.emergency.links.length > 0 && (
                  <div className="group relative overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-br from-red-900/30 via-red-800/20 to-red-900/30 backdrop-blur-sm p-8 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-red-500/20 hover:border-red-400/30">
                    <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 via-transparent to-red-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute top-4 right-4 w-2 h-2 bg-red-400 rounded-full animate-pulse" />

                    <div className="relative z-10">
                      <div className="flex items-start space-x-4 mb-6">
                        <div className="rounded-xl p-3 bg-gradient-to-r from-red-500 to-red-600 shadow-lg">
                          <AlertTriangle className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-bold text-xl text-white mb-2">
                            Emergency Access
                          </h3>
                          <p className="text-red-200 text-sm leading-relaxed">
                            Immediate security actions and emergency account
                            recovery options
                          </p>
                        </div>
                      </div>
                      <Button
                        asChild
                        className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                      >
                        <Link
                          href={app.emergency.links[0].link}
                          target="_blank"
                          className="flex items-center justify-center"
                        >
                          <AlertTriangle className="w-5 h-5 mr-3" />
                          🚨 {app.emergency.links[0].description}
                          <ExternalLink className="w-4 h-4 ml-2 opacity-70" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {app.emergency.links.length > 1 && (
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="w-4 h-4 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-white">
                    Additional Emergency Actions
                  </h2>
                  <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
                </div>
                <div className="space-y-3">
                  {app.emergency.links.slice(1).map((emergencyLink, index) => (
                    <Button
                      key={`emergency-${index}-${emergencyLink.description}`}
                      asChild
                      variant="outline"
                      className="w-full justify-start bg-red-900/20 border-red-500/30 text-red-300 hover:bg-red-800/30 hover:border-red-400/50 hover:text-red-200 transition-all duration-200 h-12"
                    >
                      <Link
                        href={emergencyLink.link}
                        target="_blank"
                        className="flex items-center"
                      >
                        <AlertTriangle className="w-4 h-4 mr-3" />
                        {emergencyLink.description}
                        <ExternalLink className="w-4 h-4 ml-auto opacity-70" />
                      </Link>
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {app.emergency.instructions.length > 0 && (
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <Activity className="w-4 h-4 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-white">
                    Security Instructions
                  </h2>
                  <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
                </div>
                <div className="space-y-6">
                  {app.emergency.instructions.map((instruction, index) => (
                    <div
                      key={`instruction-${index}-${instruction.description.slice(
                        0,
                        20
                      )}`}
                      className="rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
                    >
                      <p className="text-neutral-300 leading-relaxed mb-4">
                        {instruction.description}
                      </p>
                      {instruction.imageLink && (
                        <div className="mt-4">
                          <Image
                            src={instruction.imageLink}
                            alt="Security instruction visual guide"
                            width={400}
                            height={200}
                            className="rounded-xl border border-white/10 shadow-lg w-full object-cover"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </LiquidGlass>
      </DialogContent>
    </Dialog>
  );
};
